// Listen for messages from content scripts or popup
browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'saveToMongoDB') {
    console.log('Received save request:', request.data);

    // Handle async operation with Promise
    (async () => {
      try {
        // Get current profile name and NodeJS server URL from storage
        const result = await browser.storage.sync.get(['llmKey', 'nodeJsServer']);
        const profileName = result.llmKey || 'test';
        const serverUrl = result.nodeJsServer || 'http://localhost:3000';

        // Send data to backend server
        const response = await fetch(`${serverUrl}/save-page`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data: request.data,
            profileName: profileName
          })
        });

        console.log('Save response status:', response.status);
        const responseData = await response.json();
        console.log('Save result:', responseData);

        sendResponse({ success: true, result: responseData });
      } catch (error) {
        console.error('Save error:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();

    return true; // Will respond asynchronously
  }

  if (request.action === 'saveToMongoDB2') {
    console.log('Received save request:', request.data);

    // Handle async operation with Promise
    (async () => {
      try {
        // Get current profile name and NodeJS server URL from storage
        const result = await browser.storage.sync.get(['llmKey', 'nodeJsServer']);
        const profileName = result.llmKey || 'test';
        const serverUrl = result.nodeJsServer || 'http://localhost:3000';

        // Send data to backend server
        const response = await fetch(`${serverUrl}/cata-save`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            data: request.data,
            profileName: profileName
          })
        });

        console.log('Save response status:', response.status);
        const responseData = await response.json();
        console.log('Save result:', responseData);

        sendResponse({ success: true, result: responseData });
      } catch (error) {
        console.error('Save error:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();

    return true; // Will respond asynchronously
  }

  if (request.action === 'getCataData') {
    console.log('Received get cata data request');

    // Handle async operation with Promise
    (async () => {
      try {
        // Get current profile name and NodeJS server URL from storage
        const result = await browser.storage.sync.get(['llmKey', 'nodeJsServer']);
        const profileName = result.llmKey || 'test';
        const serverUrl = result.nodeJsServer || 'http://localhost:3000';

        // Get data from backend server
        const response = await fetch(`${serverUrl}/get-cata-data?profileName=${profileName}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        console.log('Get response status:', response.status);
        const responseData = await response.json();
        console.log('Get result:', responseData);

        sendResponse({ success: true, data: responseData.data });
      } catch (error) {
        console.error('Get error:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();

    return true; // Will respond asynchronously
  }
});

// Note: Direct MongoDB connection is not possible in browser extensions
// All MongoDB operations are handled through the Node.js backend server
// This function is kept for reference but not used in the extension