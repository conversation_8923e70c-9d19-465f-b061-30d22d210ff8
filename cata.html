<!DOCTYPE html>
<html>
  <head>
    <title>Tab Lister - Catalog</title>
    <style>
      body {
        width: 500px;
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      .tab-item {
        margin-bottom: 15px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      .tab-title {
        font-weight: bold;
        margin-bottom: 5px;
      }
      .tab-url {
        color: #666;
        font-size: 0.9em;
        margin-bottom: 5px;
        word-break: break-all;
      }
      .tag-input {
        width: 200px;
        padding: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 5px;
      }
      .number-input {
        width: 60px;
        padding: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      .date-input {
        width: 150px;
        padding: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      .input-label {
        font-weight: bold;
        margin-top: 10px;
        margin-bottom: 3px;
      }
      .back-button {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        background-color: #666;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .back-button:hover {
        background-color: #555;
      }
      .save-button {
        background-color: #4CAF50;
        color: white;
        padding: 5px 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .save-button:hover {
        background-color: #45a049;
      }
      .tag-display {
        display: inline-block;
        background-color: #e0e0e0;
        padding: 2px 8px;
        border-radius: 12px;
        margin-right: 5px;
        margin-top: 5px;
      }
      /* 标签建议相关样式 */
      .suggest-tags-btn {
        background-color: #2196F3;
        color: white;
        padding: 5px 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-top: 5px;
      }
      .suggest-tags-btn:hover {
        background-color: #0b7dda;
      }
      .suggested-tags-container {
        margin-top: 10px;
      }
      .tag {
        display: inline-block;
        background-color: #e0e0e0;
        padding: 2px 8px;
        margin: 2px;
        border-radius: 4px;
      }
      .suggested-tag {
        background-color: #e8f4ff;
      }
      .delete-tag-btn {
        margin-left: 5px;
        cursor: pointer;
        border: none;
        background: transparent;
        color: red;
      }
      .item-tags {
        margin-top: 5px;
      }
      .login-required-field {
        pointer-events: none !important;
        user-select: none !important;
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        opacity: 0.6 !important;
        background-color: #f0f0f0 !important;
        cursor: not-allowed !important;
      }
      .login-required-field-container {
        opacity: 0.7;
        position: relative;
      }
      .login-required-field-container::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: transparent;
        z-index: 10;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <button class="back-button" id="backButton">Back</button>
    <h1>Tab Catalog</h1>
    <div id="loginNotice" style="display: none; color: #F44336; margin-bottom: 10px; font-weight: bold;">
      Please login to use MongoDB features
    </div>
    
    <div style="margin-bottom: 10px;">
      <button id="saveToMongo" class="save-button">Save to MongoDB</button>
      <button id="loadCategories" class="save-button" style="background-color: #2196F3;">Load MongoDB's Category</button>
    </div>
    
    <div style="margin-bottom: 15px;">
      <button id="addToBookmark" class="save-button" style="background-color: #FF9800;">Add to Bookmark</button>
      <button id="loadFromCatai" class="save-button" style="background-color: #FF6666;">Load Catai's Category</button>
    </div>
 
    <div id="tabList"></div>
    <script src="cata.js"></script>
    <script>
    // 标签建议功能
    document.addEventListener('DOMContentLoaded', () => {
      // 监听tabList的变化，因为tab-item是动态创建的
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            setupTagSuggestions();
          }
        });
      });
      
      observer.observe(document.getElementById('tabList'), { childList: true });
      
      // 初始化标签建议功能
      function setupTagSuggestions() {
        // 为每个tab-item添加标签建议按钮和容器
        const tabItems = document.querySelectorAll('.tab-item');
        
        tabItems.forEach(item => {
          // 检查是否已经添加了标签建议按钮，避免重复添加
          if (item.querySelector('.suggest-tags-btn')) return;
          
          // 创建item-tags容器（如果不存在）
          let itemTags = item.querySelector('.tag-container');
          if (!itemTags) {
            itemTags = document.createElement('div');
            itemTags.className = 'item-tags';
            // 插入到合适的位置
            const inputGroup = item.querySelector('div[style="margin-top: 10px;"]');
            if (inputGroup) {
              item.insertBefore(itemTags, inputGroup);
            }
          }
          
          // 创建建议标签按钮
          const suggestButton = document.createElement('button');
          suggestButton.className = 'suggest-tags-btn';
          suggestButton.textContent = 'Suggest Tags';
          
          // 创建建议标签容器
          const suggestedTagsContainer = document.createElement('div');
          suggestedTagsContainer.className = 'suggested-tags-container';
          suggestedTagsContainer.style.marginTop = '10px';
          
          // 添加到DOM
          item.appendChild(suggestButton);
          item.appendChild(suggestedTagsContainer);
          
          // 添加点击事件
          suggestButton.addEventListener('click', async () => {
            // 获取标题作为上下文
            const titleElement = item.querySelector('.tab-title');
            const itemContext = titleElement ? titleElement.textContent : '';
            
            // 获取标签建议
            const suggestedTags = await mockGetTagSuggestions(itemContext);
            
            // 清空之前的建议
            suggestedTagsContainer.innerHTML = '';
            
            // 显示新的建议
            if (suggestedTags && suggestedTags.length > 0) {
              suggestedTags.forEach(tagText => {
                const tagElement = createTagElement(tagText);
                suggestedTagsContainer.appendChild(tagElement);
              });
            } else {
              suggestedTagsContainer.textContent = '没有找到合适的标签建议。';
            }
          });
        });
      }
      
      /**
       * 模拟获取标签建议的函数
       * @param {string} context - 项目的相关上下文信息
       * @returns {Promise<string[]>} - 返回一个包含建议标签字符串的数组
       */
      async function mockGetTagSuggestions(context) {
        console.log(`正在为上下文获取标签建议: "${context}"`);
        // 模拟网络请求延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 返回4个模拟标签
        // 实际应用中，这些标签应该基于 context 动态生成或从服务器获取
        return ["建议标签A", "相关标签B", "主题标签C", "热门标签D"];
      }
      
      /**
       * 创建一个标签元素（包含文本和删除按钮）
       * @param {string} tagText - 标签的文本内容
       * @returns {HTMLElement} - 返回创建的标签元素 (span)
       */
      function createTagElement(tagText) {
        const tagSpan = document.createElement('span');
        tagSpan.classList.add('tag', 'suggested-tag');
        tagSpan.style.display = 'inline-block';
        tagSpan.style.backgroundColor = '#e8f4ff';
        tagSpan.style.padding = '2px 8px';
        tagSpan.style.margin = '2px';
        tagSpan.style.borderRadius = '4px';
        
        const textNode = document.createTextNode(tagText + ' ');
        tagSpan.appendChild(textNode);
        
        const deleteButton = document.createElement('button');
        deleteButton.classList.add('delete-tag-btn');
        deleteButton.textContent = 'X';
        deleteButton.style.marginLeft = '5px';
        deleteButton.style.cursor = 'pointer';
        deleteButton.style.border = 'none';
        deleteButton.style.background = 'transparent';
        deleteButton.style.color = 'red';
        
        deleteButton.addEventListener('click', () => {
          tagSpan.remove();
        });
        
        tagSpan.appendChild(deleteButton);
        return tagSpan;
      }
    });
    </script>
  </body>
</html>


