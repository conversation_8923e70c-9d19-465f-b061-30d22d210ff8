<!DOCTYPE html>
<html>
  <head>
    <title>Tab Lister - Action</title>
    <style>
      body {
        width: 800px;
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      .button-group {
        margin-bottom: 20px;
      }
      button {
        background-color: #4CAF50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
      }
      button:hover {
        background-color: #45a049;
      }
      .back-button {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        background-color: #666;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .back-button:hover {
        background-color: #555;
      }
      .response-container {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f9f9f9;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
      .loading {
        text-align: center;
        margin-top: 20px;
        color: #666;
      }
      .error {
        color: #d32f2f;
        margin-top: 10px;
      }
      .page-info {
        margin-top: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #f5f5f5;
      }
      .page-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
      }
      .page-url {
        color: #666;
        margin-bottom: 10px;
        word-break: break-all;
      }
      .page-content {
        margin-top: 10px;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #eee;
        border-radius: 4px;
        max-height: 200px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
        font-size: 0.9em;
      }
      .save-button {
        background-color: #2196F3;
      }
      .save-button:hover {
        background-color: #1976D2;
      }
      #saveStatus {
        margin-top: 10px;
        padding: 10px;
        border-radius: 4px;
        display: none;
      }
      .success {
        background-color: #dff0d8;
        color: #3c763d;
      }
      .error {
        background-color: #f2dede;
        color: #a94442;
      }
      .login-notice {
        color: #F44336;
        margin-bottom: 10px;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="button-group">
      <button id="backButton">Back</button>
      <button id="saveButton" class="save-button">Save to MongoDB</button>
    </div>
    <h1>Tab Action</h1>
    <div id="loading" class="loading" style="display: none;">Processing...</div>
    <div id="error" class="error" style="display: none;"></div>
    <div id="saveStatus"></div>
    <div id="response" class="response-container"></div>
    
    <div class="page-info">
      <div class="page-title" id="pageTitle"></div>
      <div class="page-url" id="pageUrl"></div>
      <div class="page-content" id="pageContent"></div>
    </div>

    <script src="action.js"></script>
  </body>
</html> 
