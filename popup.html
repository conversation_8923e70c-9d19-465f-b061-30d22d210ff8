<!DOCTYPE html>
<html>
  <head>
    <title>Tab Lister</title>
    <style>
      body {
        width: 400px;
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      .settings-button, .cata-button, .action-button {
        position: absolute;
        top: 10px;
        padding: 5px 10px;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      .settings-button {
        right: 10px;
        background-color: #666;
      }
      .cata-button {
        right: 100px;
        background-color: #4CAF50;
      }
      .action-button {
        right: 190px;
        background-color: #2196F3;
      }
      .settings-button:hover {
        background-color: #555;
      }
      .cata-button:hover {
        background-color: #45a049;
      }
      .action-button:hover {
        background-color: #1976D2;
      }
    </style>
  </head>
  <body>
    <button class="settings-button" id="settingsButton">Settings</button>
    <button class="cata-button" id="cataButton">Catalogs</button>
    <button class="action-button" id="actionButton">Action</button>
    <h1>Open Tabs</h1>
    <ul id="tabList"></ul>
    <script src="popup.js"></script>
  </body>
</html>
