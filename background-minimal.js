// Minimal background script for Firefox testing
console.log('Catai-Tab background script loaded successfully');

// Simple message listener for testing
browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Received message:', request);
  
  if (request.action === 'test') {
    sendResponse({ success: true, message: 'Background script is working' });
    return;
  }
  
  // Handle other actions asynchronously
  if (request.action === 'getTabs') {
    browser.tabs.query({}).then(tabs => {
      sendResponse({ success: true, tabs: tabs });
    }).catch(error => {
      sendResponse({ success: false, error: error.message });
    });
    return true; // Will respond asynchronously
  }
  
  sendResponse({ success: false, error: 'Unknown action' });
});
