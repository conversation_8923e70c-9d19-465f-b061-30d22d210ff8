// Firefox Extension Validation Script
// Run this in Node.js to validate the extension structure

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Firefox Extension...\n');

// Required files for the extension
const requiredFiles = [
  'manifest.json',
  'background.js',
  'popup.html',
  'popup.js',
  'content.js',
  'settings.html',
  'settings.js',
  'action.html',
  'action.js',
  'cata.html',
  'cata.js',
  'firefox-compatibility.js'
];

// Check if all required files exist
console.log('📁 Checking required files:');
let allFilesExist = true;
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing!');
  process.exit(1);
}

// Validate manifest.json
console.log('\n📋 Validating manifest.json:');
try {
  const manifestContent = fs.readFileSync('manifest.json', 'utf8');
  const manifest = JSON.parse(manifestContent);
  
  // Check required fields
  const requiredFields = ['manifest_version', 'name', 'version', 'description'];
  requiredFields.forEach(field => {
    if (manifest[field]) {
      console.log(`✅ ${field}: ${manifest[field]}`);
    } else {
      console.log(`❌ ${field} - MISSING`);
    }
  });
  
  // Check Firefox-specific settings
  if (manifest.browser_specific_settings && manifest.browser_specific_settings.gecko) {
    console.log('✅ browser_specific_settings.gecko found');
    if (manifest.browser_specific_settings.gecko.id) {
      console.log(`✅ Extension ID: ${manifest.browser_specific_settings.gecko.id}`);
    } else {
      console.log('❌ Extension ID missing');
    }
  } else {
    console.log('❌ browser_specific_settings.gecko missing');
  }
  
  // Check manifest version
  if (manifest.manifest_version === 2) {
    console.log('✅ Manifest version 2 (Firefox compatible)');
  } else {
    console.log(`❌ Manifest version ${manifest.manifest_version} (should be 2 for Firefox)`);
  }
  
  // Check permissions
  if (manifest.permissions && Array.isArray(manifest.permissions)) {
    console.log(`✅ Permissions: ${manifest.permissions.join(', ')}`);
  } else {
    console.log('❌ Permissions missing or invalid');
  }
  
} catch (error) {
  console.log(`❌ manifest.json validation failed: ${error.message}`);
  process.exit(1);
}

// Check JavaScript files for syntax errors
console.log('\n🔧 Checking JavaScript syntax:');
const jsFiles = requiredFiles.filter(file => file.endsWith('.js'));
jsFiles.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    // Basic syntax check - look for common issues
    if (content.includes('chrome.') && !content.includes('browser.')) {
      console.log(`⚠️  ${file}: Contains chrome. API calls without browser. fallback`);
    } else {
      console.log(`✅ ${file}: Syntax appears valid`);
    }
  } catch (error) {
    console.log(`❌ ${file}: ${error.message}`);
  }
});

console.log('\n🎉 Validation complete!');
console.log('\n📝 Next steps:');
console.log('1. Open Firefox');
console.log('2. Go to about:debugging');
console.log('3. Click "This Firefox"');
console.log('4. Click "Load Temporary Add-on"');
console.log('5. Select the manifest.json file');
