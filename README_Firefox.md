# Catai-Tab Firefox Extension

这是 Catai-Tab 扩展的 Firefox 版本，已从 Chrome 扩展转换而来。

## 安装方法

### 方法 1: 临时安装（开发模式）

1. 打开 Firefox 浏览器
2. 在地址栏输入 `about:debugging`
3. 点击左侧的 "此 Firefox"
4. 点击 "临时载入附加组件"
5. 选择扩展文件夹中的 `manifest.json` 文件
6. 扩展将被临时安装

### 方法 2: 打包安装

1. 将整个扩展文件夹压缩为 ZIP 文件
2. 确保 ZIP 文件的根目录包含 `manifest.json` 文件
3. 在 Firefox 中访问 `about:addons`
4. 点击齿轮图标，选择 "从文件安装附加组件"
5. 选择 ZIP 文件进行安装

## 主要变更

从 Chrome 扩展转换为 Firefox 扩展时进行了以下主要变更：

### 1. Manifest 文件
- 从 Manifest V3 转换为 Manifest V2
- 添加了 Firefox 特定的 `applications.gecko` 配置
- 将 `action` 改为 `browser_action`
- 将 `service_worker` 改为 `scripts` 数组

### 2. API 调用
- 将所有 `chrome.*` API 调用替换为 `browser.*` API
- 将回调模式转换为 Promise 模式
- 修复了异步消息处理

### 3. 兼容性
- 添加了 `firefox-compatibility.js` 兼容性层
- 确保在 Firefox 和 Chrome 中都能正常工作

## 功能特性

- 标签页管理和列表显示
- 标签页分类和标签功能
- 书签管理和同步
- MongoDB 数据存储
- LLM API 集成
- 用户认证

## 系统要求

- Firefox 57.0 或更高版本
- 支持 WebExtensions API

## 故障排除

如果遇到安装问题，请按以下步骤排查：

### 1. 检查 manifest.json 格式
确保 manifest.json 文件格式正确：
- 使用 `browser_specific_settings` 而不是 `applications`
- 包含正确的 Firefox 扩展 ID
- manifest_version 设置为 2

### 2. 验证文件完整性
确保以下文件都存在：
- manifest.json
- background.js
- popup.html
- popup.js
- content.js
- settings.html
- settings.js
- action.html
- action.js
- cata.html
- cata.js
- firefox-compatibility.js

### 3. Firefox 调试步骤
1. 打开 Firefox 开发者工具 (F12)
2. 访问 `about:debugging`
3. 点击 "此 Firefox"
4. 查看扩展加载错误信息
5. 检查控制台中的 JavaScript 错误

### 4. 常见错误及解决方案

**错误: "File does not contain a valid manifest"**
- 检查 manifest.json 语法是否正确
- 确保使用 UTF-8 编码保存文件
- 验证 JSON 格式（可使用在线 JSON 验证器）

**错误: "Extension ID is required"**
- 确保 manifest.json 中包含 `browser_specific_settings.gecko.id`

**错误: "Background script failed to load"**
- 检查 background.js 文件是否存在
- 查看浏览器控制台的 JavaScript 错误
- 确保 firefox-compatibility.js 文件存在

### 5. 手动验证步骤
```bash
# 检查 manifest.json 格式
cat manifest.json | python -m json.tool

# 验证文件权限
ls -la *.js *.html *.json
```

## 开发说明

如果需要进一步开发或修改：

1. 所有 API 调用都使用 `browser.*` 命名空间
2. 异步操作使用 Promise 而不是回调
3. 兼容性层确保跨浏览器兼容性
