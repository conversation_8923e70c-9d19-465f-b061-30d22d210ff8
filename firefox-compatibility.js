// Firefox compatibility layer for Chrome extension APIs
// This file provides compatibility for Chrome APIs in Firefox

(function() {
  'use strict';

  // Ensure browser API is available
  if (typeof browser === 'undefined') {
    if (typeof chrome !== 'undefined') {
      // In Chrome, create browser namespace as alias to chrome
      window.browser = chrome;
    } else {
      console.error('Neither browser nor chrome API is available');
      return;
    }
  }

  // In Firefox, also make chrome available for compatibility
  if (typeof chrome === 'undefined' && typeof browser !== 'undefined') {
    window.chrome = browser;
  }

  // Firefox-specific fixes
  if (typeof browser !== 'undefined' && browser.runtime && browser.runtime.getBrowserInfo) {
    // This is likely Firefox

    // Fix for Firefox message passing - ensure proper async handling
    if (browser.runtime.onMessage) {
      const originalAddListener = browser.runtime.onMessage.addListener;
      browser.runtime.onMessage.addListener = function(listener) {
        const wrappedListener = function(request, sender, sendResponse) {
          const result = listener(request, sender, sendResponse);
          // If the listener returns a Promise, handle it properly
          if (result && typeof result.then === 'function') {
            result.then(sendResponse).catch(error => {
              console.error('Message handler error:', error);
              sendResponse({ error: error.message });
            });
            return true; // Keep the message channel open
          }
          return result;
        };
        return originalAddListener.call(this, wrappedListener);
      };
    }
  }

  // Ensure all APIs return Promises in Firefox
  if (typeof browser !== 'undefined') {
    // Firefox already uses Promises, but ensure consistency
    const ensurePromise = (api, method) => {
      if (api && api[method] && typeof api[method] === 'function') {
        const original = api[method];
        api[method] = function(...args) {
          const result = original.apply(this, args);
          if (result && typeof result.then === 'function') {
            return result;
          }
          return Promise.resolve(result);
        };
      }
    };

    // Ensure Promise-based APIs
    if (browser.storage && browser.storage.sync) {
      ensurePromise(browser.storage.sync, 'get');
      ensurePromise(browser.storage.sync, 'set');
      ensurePromise(browser.storage.sync, 'remove');
    }

    if (browser.tabs) {
      ensurePromise(browser.tabs, 'query');
      ensurePromise(browser.tabs, 'sendMessage');
      ensurePromise(browser.tabs, 'highlight');
      ensurePromise(browser.tabs, 'remove');
    }

    if (browser.windows) {
      ensurePromise(browser.windows, 'getCurrent');
    }

    if (browser.bookmarks) {
      ensurePromise(browser.bookmarks, 'search');
      ensurePromise(browser.bookmarks, 'create');
      ensurePromise(browser.bookmarks, 'getChildren');
      ensurePromise(browser.bookmarks, 'get');
    }
  }
})();
