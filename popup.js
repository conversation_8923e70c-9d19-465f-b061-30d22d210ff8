document.addEventListener('DOMContentLoaded', () => {
  // Add settings button click handler
  document.getElementById('settingsButton').addEventListener('click', () => {
    window.location.href = 'settings.html';
  });

  // Add cata button click handler
  document.getElementById('cataButton').addEventListener('click', () => {
    window.location.href = 'cata.html';
  });

  // Add action button click handler
  document.getElementById('actionButton').addEventListener('click', () => {
    window.location.href = 'action.html';
  });

  browser.tabs.query({}).then((tabs) => {
    const tabList = document.getElementById('tabList');
    tabs.forEach((tab) => {
      const listItem = document.createElement('li');
      listItem.innerHTML = `<b>${tab.title}</b>: <a href="${tab.url}" target="_blank">${tab.url}</a>`;
      tabList.appendChild(listItem);
    });
  });
});
