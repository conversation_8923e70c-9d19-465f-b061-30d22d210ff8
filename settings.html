<!DOCTYPE html>
<html>
  <head>
    <title>Tab Lister Settings</title>
    <style>
      body {
        width: 400px;
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      .form-group {
        margin-bottom: 15px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }
      input[type="text"], input[type="password"], input[type="email"], textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      textarea {
        min-height: 100px;
        resize: vertical;
      }
      button {
        background-color: #4CAF50;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #45a049;
      }
      .back-button {
        background-color: #666;
        margin-right: 10px;
      }
      .test-button {
        background-color: #2196F3;
        margin-left: 10px;
      }
      .test-button:hover {
        background-color: #1976D2;
      }
      .button-group {
        margin-top: 20px;
      }
      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
      }
      .modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 20px;
        border-radius: 4px;
        width: 80%;
        max-width: 500px;
        position: relative;
      }
      .close-button {
        position: absolute;
        right: 10px;
        top: 10px;
        font-size: 20px;
        cursor: pointer;
        color: #666;
      }
      .response-content {
        margin-top: 15px;
        padding: 10px;
        background-color: #f5f5f5;
        border-radius: 4px;
        white-space: pre-wrap;
        word-wrap: break-word;
        max-height: 300px;
        overflow-y: auto;
      }
      .auth-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 10px;
        background-color: #f5f5f5;
        border-radius: 4px;
      }
      .auth-status {
        font-weight: bold;
      }
      .login-button {
        background-color: #2196F3;
      }
      .signup-button {
        background-color: #FF9800;
      }
      .logout-button {
        background-color: #F44336;
      }
      .auth-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 1000;
      }
      .auth-modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 20px;
        border-radius: 4px;
        width: 80%;
        max-width: 400px;
        position: relative;
      }
      .auth-form-group {
        margin-bottom: 15px;
      }
      .auth-form-group label {
        display: block;
        margin-bottom: 5px;
      }
      .auth-form-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
      .auth-button-group {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <h1>Tab Settings v3.01</h1>
    
    <div class="button-group" style="margin-bottom: 20px;">
      <button class="back-button" id="backButton">Back</button>
      <button id="applyButton">Apply</button>
    </div>
    
    <!-- Authentication Container -->
    <div class="auth-container">
      <div class="auth-status" id="authStatus">Not logged in</div>
      <div>
        <button id="loginButton" class="login-button">Login</button>
        <button id="signupButton" class="signup-button">Sign Up</button>
        <button id="logoutButton" class="logout-button" style="display: none;">Logout</button>
      </div>
    </div>
    
    <!-- Login Modal -->
    <div id="loginModal" class="auth-modal">
      <div class="auth-modal-content">
        <span class="close-button" id="closeLoginModal">&times;</span>
        <h2>Login</h2>
        <div class="auth-form-group">
          <label for="loginEmail">Email/Phone:</label>
          <input type="text" id="loginEmail" placeholder="Enter your email or phone" value="<EMAIL>">
        </div>
        <div class="auth-form-group">
          <label for="loginPassword">Password:</label>
          <input type="password" id="loginPassword" placeholder="Enter your password" value="api2025#">
        </div>
        <div class="auth-button-group">
          <button id="cancelLogin" class="back-button">Cancel</button>
          <button id="submitLogin" class="login-button">Login</button>
        </div>
      </div>
    </div>
    
    <!-- Sign Up Modal -->
    <div id="signupModal" class="auth-modal">
      <div class="auth-modal-content">
        <span class="close-button" id="closeSignupModal">&times;</span>
        <h2>Sign Up</h2>
        <div class="auth-form-group">
          <label for="signupEmail">Email/Phone:</label>
          <input type="text" id="signupEmail" placeholder="Enter your email or phone" value="<EMAIL>">
        </div>
        <div class="auth-form-group">
          <label for="signupPassword">Password:</label>
          <input type="password" id="signupPassword" placeholder="Enter your password" value="api2025#">
        </div>
        <div class="auth-button-group">
          <button id="cancelSignup" class="back-button">Cancel</button>
          <button id="submitSignup" class="signup-button">Sign Up</button>
        </div>
      </div>
    </div>
    <div class="form-group">
      <label for="llmKey">LLM API Profile:</label>
      <select id="llmKey">
        <option value="">Select a profile</option>
        <option value="profile1">Profile 1</option>
        <option value="profile2">Profile 2</option>
      </select>
      <div style="margin-top: 10px;">
        <input type="text" id="newProfileName" placeholder="Enter new profile name">
        <button id="newProfileButton" style="margin-left: 5px;">New</button>
        <button id="saveProfileButton" style="margin-left: 5px;">Save</button>
      </div>
    </div>
    <div class="form-group">
      <label for="baseUrl">Base URL:</label>
      <input type="text" id="baseUrl" placeholder="Enter the base URL">
    </div>
    <div class="form-group">
      <label for="token">Token:</label>
      <input type="text" id="token" placeholder="Enter your token">
    </div>
    <div class="form-group">
      <label for="model">Model:</label>
      <input type="text" id="model" placeholder="Enter model name">
    </div>
    
    <div class="button-group" style="margin: 15px 0;">
      <button class="test-button" id="testButton">Test LLM Connection</button>
    </div>
    
    <div class="form-group">
      <label for="prompt">Prompt:</label>
      <textarea id="prompt" placeholder="Enter your prompt template"></textarea>
    </div>
    <div class="form-group">
      <label for="nodeJsServer">NodeJS Server:</label>
      <input type="text" id="nodeJsServer" placeholder="Enter NodeJS server URL">
    </div>
    <div class="form-group">
      <label for="mongoUrl">MongoDB Connection URL:</label>
      <input type="text" id="mongoUrl" placeholder="Enter MongoDB connection URL">
      <button id="testMongo" class="test-button">Test MongoDB Connection</button>
    </div>

    <!-- <div class="button-group">
      <button class="back-button" id="backButton">Back</button>
      <button id="applyButton">Apply</button>
      <button class="test-button" id="testButton">Test Connection</button>
    </div> -->

    <!-- Test Response Modal -->
    <div id="testModal" class="modal">
      <div class="modal-content">
        <span class="close-button" id="closeModal">&times;</span>
        <h2>Test Response</h2>
        <div id="testResponse" class="response-content"></div>
      </div>
    </div>

    <script src="settings.js"></script>
  </body>
</html>



