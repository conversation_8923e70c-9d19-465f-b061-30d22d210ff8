// Function to get all text content from the page
function getPageContent() {
  // Get the main content
  const bodyText = document.body.innerText;
  
  // Get all text from input fields
  const inputTexts = Array.from(document.getElementsByTagName('input'))
    .map(input => input.value)
    .filter(value => value.trim().length > 0)
    .join('\n');

  // Get all text from textareas
  const textareaTexts = Array.from(document.getElementsByTagName('textarea'))
    .map(textarea => textarea.value)
    .filter(value => value.trim().length > 0)
    .join('\n');

  // Combine all text content
  const allContent = [
    bodyText,
    inputTexts,
    textareaTexts
  ].filter(text => text.trim().length > 0).join('\n\n');

  return allContent;
}

// Listen for messages from the extension
browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getPageContent') {
    try {
      const content = getPageContent();
      sendResponse({
        content,
        success: true,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      sendResponse({
        error: error.message,
        success: false,
        timestamp: new Date().toISOString()
      });
    }
  }
  return true; // Keep the message channel open for async response
});