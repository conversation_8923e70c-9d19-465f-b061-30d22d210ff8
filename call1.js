const options = {
    method: 'POST',
    headers: {Authorization: 'Bearer <token>', 'Content-Type': 'application/json'},
    body: '{"model":"<Model>","messages":[{"role":"user","content":"<Prompt>"}],"stream":false,"max_tokens":512,"enable_thinking":false,"thinking_budget":4096,"min_p":0.05,"stop":null,"temperature":0.7,"top_p":0.7,"top_k":50,"frequency_penalty":0.5,"n":1,"response_format":{"type":"text"},"tools":[{"type":"function","function":{"description":"<string>","name":"<string>","parameters":{},"strict":false}}]}'
  };
  
  fetch('<OPENAI_BASE_URL>/chat/completions', options)
    .then(response => response.json())
    .then(response => console.log(response))
    .catch(err => console.error(err));